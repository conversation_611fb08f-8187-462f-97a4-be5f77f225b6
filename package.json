{"name": "app-api", "version": "0.0.1", "engines": {"node": "20.11.1", "npm": "10.2.4"}, "description": "App API for Wealthyhood Investment Platform", "private": true, "scripts": {"accounting:generate-xml": "ts-node src/scripts/accounting/generate-xml.ts", "accounting:generate-csv": "ts-node src/scripts/accounting/generate-csv.ts", "accounting:generate-invoice-csv": "ts-node src/scripts/accounting/generate-invoice-csv.ts", "accounting:validate-db-ledger": "ts-node src/scripts/accounting/validate-db-ledger.ts", "accounting:validate-accounts-cash": "ts-node src/scripts/accounting/validate-accounts-cash.ts", "start": "npm run serve", "cron": "node dist/jobs/cronJobScheduler.js", "serve": "node dist/server.js", "build": "npm run build-ts", "build:prod": "npm run build-ts && npm run sentry:sourcemaps", "build-and-lint": "npm run build-ts && npm run lint-fix", "build-ts": "tsc -p .", "build-render": "rm -rf node_modules/ && npm cache clean --force && npm install --production=false && npm run build", "build-render:prod": "rm -rf node_modules/ && npm cache clean --force && npm install --production=false && npm run build:prod", "email:dev": "email dev --dir 'src/scripts/email-builder'", "watch": "concurrently -k -p \"[{name}]\" -n \"Node,Assets\" -c \"cyan.bold,green.bold\" \"npm run watch-node\"", "watch-node": "ts-node-dev --respawn --debug --transpile-only --inspect=0.0.0.0:9229 -- ./src/server.ts", "lint-fix": "tsc --noEmit && eslint \"**/*.{js,ts}\" --quiet --fix", "lint": "tsc --noEmit && eslint \"**/*.{js,ts}\"", "test": "jest --forceExit --useStderr --detectO<PERSON>Handles", "test-ci": "jest --forceExit --useStderr --silent --maxWorkers=4", "sample": "npm run build-ts && node ./data/loadSampleData.js", "prepare": "husky install", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org wealthyhood --project app-api ./dist && sentry-cli sourcemaps upload --org wealthyhood --project app-api ./dist", "debug-test": "node --inspect-brk=0.0.0.0:9227 ./node_modules/jest/bin/jest.js --testTimeout=300000 --runInBand --config=jest.config.js"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.19", "@aws-sdk/client-s3": "^3.650.0", "@aws-sdk/lib-storage": "^3.650.0", "@contentful/node-apps-toolkit": "^3.13.1", "@contentful/rich-text-html-renderer": "^17.0.1", "@ladjs/graceful": "^4.2.0", "@libsql/client": "^0.15.9", "@react-email/components": "^0.1.1", "@react-email/render": "^1.1.2", "@segment/analytics-node": "^1.3.0", "@sentry/cli": "^2.46.0", "@sentry/node": "^9.17.0", "@sentry/profiling-node": "^9.32.0", "@socket.io/redis-adapter": "^8.3.0", "@socket.io/redis-emitter": "^5.1.0", "@types/adm-zip": "^0.5.7", "@types/json-patch": "^0.0.33", "@types/socket.io": "^3.0.2", "@upstash/redis": "^1.35.0", "@wealthyhood/shared-configs": "^1.14.39", "@webcarrot/xirr": "^3.0.1", "adm-zip": "^0.5.16", "ai": "^4.3.16", "auth0": "^4.8.0", "axios": "^1.10.0", "body-parser": "~1.20.3", "bree": "^9.2.4", "commander": "^12.1.0", "configcat-node": "^10.1.1", "contentful": "^11.7.3", "contentful-management": "^11.54.0", "cors": "^2.8.5", "csv-parse": "^5.6.0", "csv-stringify": "^6.5.2", "debug": "^4.4.1", "decimal.js": "^10.5.0", "disposable-email-domains": "^1.0.59", "dotenv": "^16.5.0", "express": "^4.21.2", "express-jwt": "^8.5.1", "express-openid-connect": "^2.18.1", "express-session": "^1.18.1", "gaussian": "^1.2.0", "html-to-text": "^9.0.5", "iconv-lite": "^0.6.3", "ioredis": "^5.6.1", "jsdom": "^25.0.0", "jspdf": "^3.0.0", "jspdf-autotable": "^5.0.2", "jwks-rsa": "^3.2.0", "libhoney": "^4.3.1", "libsql": "^0.5.13", "luxon": "^3.6.1", "marked": "^15.0.12", "mongodb-memory-server": "^10.1.4", "mongoose": "^7.8.3", "mongoose-sequence": "^6.0.1", "nanoid": "^3.1.22", "node-segfault-handler": "^1.4.2", "pako": "^2.1.0", "postmark": "^4.0.5", "qs": "^6.14.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-email": "^4.0.17", "semver": "^7.7.2", "socket.io": "^4.8.1", "stripe": "^17.7.0", "truelayer-client": "^1.3.2", "truelayer-signing": "^0.1.8", "validator": "^13.15.15", "winston": "^3.13.0", "xlsx": "^0.18.5", "zod": "^3.25.67"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@sentry/types": "^9.17.0", "@types/cors": "^2.8.19", "@types/debug": "^4.1.12", "@types/disposable-email-domains": "^1.0.6", "@types/express": "^4.17.1", "@types/express-jwt": "^7.4.4", "@types/express-session": "^1.18.2", "@types/gaussian": "^1.2.0", "@types/html-to-text": "^9.0.4", "@types/jest": "^29.5.14", "@types/jsdom": "^21.1.7", "@types/json-schema": "^7.0.15", "@types/luxon": "^3.6.2", "@types/mongodb": "^4.0.7", "@types/mongoose-sequence": "^3.0.11", "@types/node": "^22.15.30", "@types/pako": "^2.0.3", "@types/qs": "^6.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/safe-timers": "^1.1.2", "@types/semver": "^7.7.0", "@types/supertest": "^6.0.3", "@types/swagger-ui-express": "^4.1.3", "@types/validator": "^13.15.2", "@types/yamljs": "^0.2.31", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.35.0", "concurrently": "^9.2.0", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "fast-xml-parser": "^5.2.5", "husky": "^7.0.2", "jest": "^29.7.0", "prettier": "^3.6.1", "strip-json-comments": "^5.0.2", "supertest": "^7.1.1", "swagger-ui-express": "^4.3.0", "ts-jest": "^29.3.4", "ts-node-dev": "^1.1.6", "typescript": "^5.7.3", "wait-for-expect": "^3.0.2", "xmlbuilder2": "^3.1.1", "yamljs": "^0.3.0"}, "resolutions": {"react-dom/server": "react-dom/server.browser"}}